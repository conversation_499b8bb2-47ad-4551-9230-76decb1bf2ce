{"name": "atlas", "type": "module", "version": "3.3.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "remove-keystatic": "node ./scripts/remove-keystatic.js", "format": "prettier -w \"**/*\" --ignore-unknown --cache", "lint": "eslint ."}, "dependencies": {"@astrojs/mdx": "4.3.3", "@astrojs/react": "4.3.0", "@astrojs/rss": "4.0.12", "@astrojs/sitemap": "3.4.2", "@astrojs/vercel": "^8.2.5", "@fontsource/poppins": "5.2.5", "@keystatic/astro": "5.0.6", "@keystatic/core": "0.5.47", "@tabler/icons": "3.31.0", "@tailwindcss/forms": "0.5.10", "@tailwindcss/vite": "4.1.4", "@types/react": "18.3.20", "@types/react-dom": "18.3.5", "animejs": "3.2.2", "astro": "5.12.8", "astro-auto-import": "0.4.4", "astro-icon": "1.1.5", "astro-seo": "0.8.4", "react": "18.3.1", "react-dom": "18.3.1", "swiper": "11.2.6", "tailwindcss": "4.1.4", "tailwindcss-animate": "1.0.7", "tarteaucitronjs": "^1.22.0"}, "devDependencies": {"@eslint/js": "9.25.1", "@playform/compress": "0.1.9", "@types/animejs": "^3.1.13", "eslint": "9.25.1", "eslint-plugin-astro": "1.3.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.6", "globals": "16.0.0", "lodash.debounce": "4.0.8", "lodash.throttle": "4.1.1", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "0.6.11", "typescript-eslint": "8.31.0", "vite-tsconfig-paths": "^5.1.4"}}