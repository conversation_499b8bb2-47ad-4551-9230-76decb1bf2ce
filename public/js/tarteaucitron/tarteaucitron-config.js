/* eslint-disable no-dupe-keys */
/* eslint-disable no-undef */
/**
 * Configurazione tarteaucitronjs per Atlas - Dott. Emanuel<PERSON>
 * Sistema di gestione cookie conforme GDPR con personalizzazioni italiane
 */

// Inizializzazione tarteaucitronjs
tarteaucitron.init({
    // Configurazione privacy
    "privacyUrl": "/privacy-policy", // URL della privacy policy
    "hashtag": "#tarteaucitron", // Hashtag per aprire il pannello
    "cookieName": "tarteaucitron", // Nome del cookie di consenso
    
    // Orientamento e posizione
    "orientation": "middle", // Posizione del banner: top, middle, bottom
    "groupServices": false, // Raggruppa servizi per categoria
    "showDetailsOnClick": true, // Mostra dettagli al click
    "serviceDefaultState": "wait", // Stato di default: wait, allow, deny
    
    // Comportamento
    "showAlertSmall": false, // Banner piccolo dopo il primo consenso
    "cookieslist": false, // Mostra lista cookie nella privacy policy
    "closePopup": false, // Chiude popup dopo consenso
    "showIcon": true, // Mostra icona floating
    "iconPosition": "BottomRight", // Posizione icona: BottomRight, BottomLeft, TopRight, TopLeft
    
    // Personalizzazione UI
    "adblocker": false, // Rileva adblocker
    "DenyAllCta": true, // Mostra pulsante "Rifiuta tutto"
    "AcceptAllCta": true, // Mostra pulsante "Accetta tutto"
    "highPrivacy": true, // Modalità alta privacy
    "handleBrowserDNTRequest": false, // Gestisce Do Not Track
    "removeCredit": false, // Rimuove crediti tarteaucitron
    "moreInfoLink": true, // Link "maggiori informazioni"
    "useExternalCss": false, // Usa CSS esterno
    "useExternalJs": false, // Usa JS esterno
    
    // Configurazione avanzata
    "readmoreLink": "/privacy-policy", // Link per saperne di più
    "mandatory": true, // Cookie tecnici obbligatori
    "mandatoryCta": true, // Mostra info sui cookie obbligatori
    
    // Personalizzazione colori (Brand Atlas)
    "bodyPosition": "bottom" // Posizione del corpo del banner
});

// Personalizzazione CSS per brand Atlas
const atlasStyles = `
    <style>
        /* Colori brand Atlas */
        :root {
            --atlas-primary: #006A71;
            --atlas-warm-white: #F8F7F4;
            --atlas-dark-gray: #212121;
            --atlas-light-gray: #6B7280;
        }
        
        /* Banner principale */
        #tarteaucitronRoot #tarteaucitronAlertBig {
            background: var(--atlas-warm-white) !important;
            border: 1px solid #E5E7EB !important;
            border-radius: 12px !important;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
            color: var(--atlas-dark-gray) !important;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif !important;
        }
        
        /* Titolo */
        #tarteaucitronRoot #tarteaucitronAlertBig h1 {
            color: var(--atlas-dark-gray) !important;
            font-weight: 600 !important;
            font-size: 1.25rem !important;
            margin-bottom: 0.75rem !important;
        }
        
        /* Testo descrizione */
        #tarteaucitronRoot #tarteaucitronAlertBig p {
            color: var(--atlas-light-gray) !important;
            font-size: 0.95rem !important;
            line-height: 1.6 !important;
            margin-bottom: 1rem !important;
        }
        
        /* Pulsanti */
        #tarteaucitronRoot .tarteaucitronCTAs button {
            border-radius: 8px !important;
            font-weight: 500 !important;
            font-size: 0.9rem !important;
            padding: 0.75rem 1.5rem !important;
            transition: all 0.2s ease !important;
            border: none !important;
            cursor: pointer !important;
            font-family: 'Poppins', sans-serif !important;
        }
        
        /* Pulsante Accetta tutto */
        #tarteaucitronRoot #tarteaucitronPersonalize,
        #tarteaucitronRoot #tarteaucitronAllAllowed {
            background: var(--atlas-primary) !important;
            color: white !important;
        }
        
        #tarteaucitronRoot #tarteaucitronPersonalize:hover,
        #tarteaucitronRoot #tarteaucitronAllAllowed:hover {
            background: #005A61 !important;
            transform: translateY(-1px) !important;
        }
        
        /* Pulsante Rifiuta tutto */
        #tarteaucitronRoot #tarteaucitronAllDenied {
            background: transparent !important;
            color: var(--atlas-light-gray) !important;
            border: 1px solid #D1D5DB !important;
        }
        
        #tarteaucitronRoot #tarteaucitronAllDenied:hover {
            background: #F9FAFB !important;
            color: var(--atlas-dark-gray) !important;
            border-color: var(--atlas-primary) !important;
        }
        
        /* Link privacy policy */
        #tarteaucitronRoot a {
            color: var(--atlas-primary) !important;
            text-decoration: underline !important;
        }
        
        #tarteaucitronRoot a:hover {
            color: #005A61 !important;
        }
        
        /* Icona floating */
        #tarteaucitronRoot #tarteaucitronIcon {
            background: var(--atlas-primary) !important;
            border-radius: 50% !important;
            box-shadow: 0 4px 12px rgba(0, 106, 113, 0.3) !important;
        }
        
        /* Panel servizi */
        #tarteaucitronRoot #tarteaucitronServices {
            background: var(--atlas-warm-white) !important;
            border-radius: 12px !important;
            border: 1px solid #E5E7EB !important;
        }
        
        /* Toggle switches */
        #tarteaucitronRoot .tarteaucitronSwitch {
            background: #D1D5DB !important;
        }
        
        #tarteaucitronRoot .tarteaucitronSwitch.tarteaucitronIsAllowed {
            background: var(--atlas-primary) !important;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            #tarteaucitronRoot #tarteaucitronAlertBig {
                margin: 1rem !important;
                border-radius: 12px !important;
            }
            
            #tarteaucitronRoot .tarteaucitronCTAs {
                flex-direction: column !important;
                gap: 0.75rem !important;
            }
            
            #tarteaucitronRoot .tarteaucitronCTAs button {
                width: 100% !important;
                justify-content: center !important;
            }
        }
    </style>
`;

// Inserisce gli stili personalizzati
document.head.insertAdjacentHTML('beforeend', atlasStyles);

// Traduzioni italiane personalizzate
tarteaucitron.lang = {
    "middleBarHead": "🍪 Gestione Cookie",
    "adblock": "Ciao! Questo sito è gratuito e si basa sulla pubblicità.<br/>Per visualizzare questo contenuto, disabilita il tuo ad-blocker e ricarica la pagina.",
    "adblock_call": "Disabilita il tuo ad-blocker",
    "reload": "Ricarica la pagina",
    
    "alertBigClick": "Continuando a navigare, accetti l'utilizzo dei cookie.",
    "alertBigPrivacy": "Questo sito utilizza cookie per migliorare la tua esperienza di navigazione e per finalità statistiche.",
    "alertBig": "Utilizziamo i cookie per personalizzare contenuti e annunci, fornire funzionalità dei social media e analizzare il nostro traffico.",
    
    "acceptAll": "Accetta tutto",
    "personalize": "Personalizza",
    "denyAll": "Rifiuta tutto",
    
    "privacyPolicyLink": "Informativa Privacy",
    "cookieDetail": "Dettaglio cookie",
    "ourSite": "sul nostro sito",
    "empty": "Nessun servizio da visualizzare.",
    
    "allow": "Consenti",
    "deny": "Nega",
    "noCookie": "Questo servizio non deposita cookie.",
    "useCookie": "Questo servizio può depositare",
    "useCookieCurrent": "Questo servizio ha depositato",
    "useNoCookie": "Questo servizio non ha depositato cookie.",
    "more": "Scopri di più",
    "source": "Visualizza il sito ufficiale",
    "credit": "Gestione cookie da tarteaucitron.js",
    
    "toggleInfoBox": "Mostra/nascondi informazioni sui cookie",
    "title": "Pannello di gestione dei cookie",
    "modalWindow": "(finestra modale)",

    "mandatory": "Cookie obbligatori",
    "mandatoryText": "Questi cookie sono necessari per il corretto funzionamento del sito e non possono essere disattivati.",

    "close": "Chiudi",
    "all": "Preferenze per tutti i servizi",

    "info": "Protezione della privacy",
    "disclaimer": "Autorizzando questi servizi di terze parti, accetti il deposito e la lettura di cookie e l'utilizzo di tecnologie di tracciamento necessarie al loro corretto funzionamento.",
    "fallback": "è disabilitato.",
    "allowed": "autorizzato",
    "disallowed": "vietato"
};

// Configurazione servizi specifici per Atlas
// ========================================

// 1. CALENDLY - Servizio di prenotazione (RICHIEDE CONSENSO)
// Calendly deposita cookie per funzionalità di prenotazione
tarteaucitron.user.calendlyUrl = 'https://calendly.com/emanuelebelloni0'; // URL base Calendly

// Configurazione personalizzata per Calendly
(tarteaucitron.job = tarteaucitron.job || []).push('calendly');

// 2. GOOGLE ANALYTICS 4 - Analytics (RICHIEDE CONSENSO)
// Se in futuro verrà aggiunto GA4, decommentare:
// tarteaucitron.user.gtagUa = 'G-XXXXXXXXXX'; // Sostituire con il tuo GA4 ID
// (tarteaucitron.job = tarteaucitron.job || []).push('gtag');

// 3. MAILERLITE - Newsletter (RICHIEDE CONSENSO)
// Se in futuro verrà integrato MailerLite, decommentare:
// tarteaucitron.user.mailerliteId = 'XXXXXXXX'; // Sostituire con il tuo ID MailerLite
// (tarteaucitron.job = tarteaucitron.job || []).push('mailerlite');

// 4. SERVIZI LOCALI (NON RICHIEDONO CONSENSO)
// - Poppins Fonts: caricati localmente da @fontsource
// - Swiper.js: libreria locale per carousel
// - AOS: animazioni locali
// - Astro View Transitions: funzionalità nativa
// - Tabler Icons: icone locali

// 5. SERVIZI ESTERNI POTENZIALI (da configurare se aggiunti)
// YouTube: (tarteaucitron.job = tarteaucitron.job || []).push('youtube');
// Google Maps: (tarteaucitron.job = tarteaucitron.job || []).push('googlemaps');
// Facebook Pixel: (tarteaucitron.job = tarteaucitron.job || []).push('facebookpixel');

// SERVIZI PERSONALIZZATI PER ATLAS
// =================================

// Servizio personalizzato per Calendly
tarteaucitron.services.calendly = {
    "key": "calendly",
    "type": "other",
    "name": "Calendly",
    "uri": "https://calendly.com/privacy",
    "needConsent": true,
    "cookies": ['__cfduid', '_calendly_session', 'calendly_session'],
    "js": function () {
        "use strict";

        // Carica lo script Calendly solo dopo il consenso
        if (typeof window.Calendly === 'undefined') {
            tarteaucitron.addScript('https://assets.calendly.com/assets/external/widget.js', 'calendly-widget-js', function() {
                console.log('📅 Calendly script caricato');

                // Inizializza tutti i widget Calendly presenti nella pagina
                const calendlyWidgets = document.querySelectorAll('.calendly-inline-widget[data-url]');
                calendlyWidgets.forEach(function(widget) {
                    const url = widget.getAttribute('data-url');
                    if (url && window.Calendly && window.Calendly.initInlineWidget) {
                        window.Calendly.initInlineWidget({
                            url: url,
                            parentElement: widget
                        });
                    }
                });
            });
        }
    },
    "fallback": function () {
        "use strict";

        // Fallback quando Calendly è bloccato
        const calendlyContainers = document.querySelectorAll('#calendly-container, .calendly-inline-widget');
        calendlyContainers.forEach(function(container) {
            container.innerHTML = `
                <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-base-50 border border-base-200 rounded-lg">
                    <div class="mb-4">
                        <svg class="w-12 h-12 mx-auto text-base-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-base-700 mb-2">Calendly bloccato</h3>
                    <p class="text-base-500 mb-4">Per prenotare un appuntamento, accetta i cookie di Calendly.</p>
                    <button onclick="tarteaucitron.userInterface.openPanel();" class="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                        Gestisci Cookie
                    </button>
                    <p class="text-sm text-base-400 mt-4">
                        Oppure contattaci direttamente:
                        <a href="mailto:<EMAIL>" class="text-primary-500 underline"><EMAIL></a>
                    </p>
                </div>
            `;
        });
    }
};

console.log('🍪 tarteaucitron.js configurato per Atlas - Dott. Emanuele Belloni');
console.log('📋 Servizi configurati:', tarteaucitron.job || []);
