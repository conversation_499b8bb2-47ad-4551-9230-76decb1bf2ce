/**
 * <PERSON><PERSON> tarteaucitronjs per Atlas
 * Brand: <PERSON> (#006A71), <PERSON> (#F8F7F4), <PERSON> (#212121)
 */

/* Variabili CSS per brand Atlas */
:root {
    --atlas-primary: #006A71;
    --atlas-primary-hover: #005A61;
    --atlas-warm-white: #F8F7F4;
    --atlas-dark-gray: #212121;
    --atlas-light-gray: #6B7280;
    --atlas-border: #E5E7EB;
    --atlas-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* BANNER PRINCIPALE */
#tarteaucitronRoot #tarteaucitronAlertBig {
    background: var(--atlas-warm-white) !important;
    border: 1px solid var(--atlas-border) !important;
    border-radius: 16px !important;
    box-shadow: var(--atlas-shadow) !important;
    color: var(--atlas-dark-gray) !important;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
}

/* Titolo banner */
#tarteaucitronRoot #tarteaucitronAlertBig h1 {
    color: var(--atlas-dark-gray) !important;
    font-weight: 600 !important;
    font-size: 1.5rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
}

/* Testo descrizione */
#tarteaucitronRoot #tarteaucitronAlertBig p {
    color: var(--atlas-light-gray) !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    margin-bottom: 1.5rem !important;
    text-align: center !important;
}

/* Container pulsanti */
#tarteaucitronRoot .tarteaucitronCTAs {
    display: flex !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    margin-top: 1.5rem !important;
}

/* Stile base pulsanti */
#tarteaucitronRoot .tarteaucitronCTAs button {
    border-radius: 12px !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    padding: 0.875rem 2rem !important;
    transition: all 0.2s ease !important;
    border: none !important;
    cursor: pointer !important;
    font-family: 'Poppins', sans-serif !important;
    min-width: 140px !important;
}

/* Pulsante Accetta tutto - Primario */
#tarteaucitronRoot #tarteaucitronPersonalize,
#tarteaucitronRoot #tarteaucitronAllAllowed {
    background: var(--atlas-primary) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(0, 106, 113, 0.3) !important;
}

#tarteaucitronRoot #tarteaucitronPersonalize:hover,
#tarteaucitronRoot #tarteaucitronAllAllowed:hover {
    background: var(--atlas-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(0, 106, 113, 0.4) !important;
}

/* Pulsante Rifiuta tutto - Secondario */
#tarteaucitronRoot #tarteaucitronAllDenied {
    background: transparent !important;
    color: var(--atlas-light-gray) !important;
    border: 2px solid var(--atlas-border) !important;
}

#tarteaucitronRoot #tarteaucitronAllDenied:hover {
    background: var(--atlas-warm-white) !important;
    color: var(--atlas-dark-gray) !important;
    border-color: var(--atlas-primary) !important;
    transform: translateY(-1px) !important;
}

/* Link privacy policy */
#tarteaucitronRoot a {
    color: var(--atlas-primary) !important;
    text-decoration: underline !important;
    font-weight: 500 !important;
}

#tarteaucitronRoot a:hover {
    color: var(--atlas-primary-hover) !important;
}

/* ICONA FLOATING */
#tarteaucitronRoot #tarteaucitronIcon {
    background: var(--atlas-primary) !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 12px rgba(0, 106, 113, 0.3) !important;
    width: 60px !important;
    height: 60px !important;
    transition: all 0.3s ease !important;
}

#tarteaucitronRoot #tarteaucitronIcon:hover {
    background: var(--atlas-primary-hover) !important;
    transform: scale(1.1) !important;
    box-shadow: 0 6px 16px rgba(0, 106, 113, 0.4) !important;
}

/* PANNELLO SERVIZI */
#tarteaucitronRoot #tarteaucitronServices {
    background: var(--atlas-warm-white) !important;
    border-radius: 16px !important;
    border: 1px solid var(--atlas-border) !important;
    box-shadow: var(--atlas-shadow) !important;
    font-family: 'Poppins', sans-serif !important;
}

#tarteaucitronRoot #tarteaucitronServices h2 {
    color: var(--atlas-dark-gray) !important;
    font-weight: 600 !important;
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
}

/* Elementi servizi */
#tarteaucitronRoot .tarteaucitronLine {
    border-bottom: 1px solid var(--atlas-border) !important;
    padding: 1rem !important;
}

#tarteaucitronRoot .tarteaucitronName {
    color: var(--atlas-dark-gray) !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
}

#tarteaucitronRoot .tarteaucitronListCookies {
    color: var(--atlas-light-gray) !important;
    font-size: 0.875rem !important;
}

/* Toggle switches */
#tarteaucitronRoot .tarteaucitronSwitch {
    background: var(--atlas-border) !important;
    border-radius: 20px !important;
    transition: all 0.2s ease !important;
}

#tarteaucitronRoot .tarteaucitronSwitch.tarteaucitronIsAllowed {
    background: var(--atlas-primary) !important;
}

#tarteaucitronRoot .tarteaucitronSwitch .tarteaucitronSlider {
    background: white !important;
    border-radius: 50% !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
    #tarteaucitronRoot #tarteaucitronAlertBig {
        margin: 1rem !important;
        padding: 1.5rem !important;
        border-radius: 16px !important;
    }
    
    #tarteaucitronRoot #tarteaucitronAlertBig h1 {
        font-size: 1.25rem !important;
    }
    
    #tarteaucitronRoot #tarteaucitronAlertBig p {
        font-size: 0.9rem !important;
    }
    
    #tarteaucitronRoot .tarteaucitronCTAs {
        flex-direction: column !important;
        gap: 0.75rem !important;
    }
    
    #tarteaucitronRoot .tarteaucitronCTAs button {
        width: 100% !important;
        justify-content: center !important;
        min-width: unset !important;
    }
    
    /* Icona floating - evita sovrapposizione con sticky bar mobile */
    #tarteaucitronRoot #tarteaucitronIcon {
        bottom: 90px !important;
        width: 50px !important;
        height: 50px !important;
    }
}

@media (max-width: 480px) {
    #tarteaucitronRoot #tarteaucitronAlertBig {
        margin: 0.75rem !important;
        padding: 1.25rem !important;
    }
    
    #tarteaucitronRoot .tarteaucitronCTAs button {
        padding: 0.75rem 1.5rem !important;
        font-size: 0.9rem !important;
    }
}

/* ANIMAZIONI */
#tarteaucitronRoot * {
    transition: all 0.2s ease !important;
}

/* Animazione di entrata per il banner */
#tarteaucitronRoot #tarteaucitronAlertBig {
    animation: slideInUp 0.3s ease-out !important;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ACCESSIBILITÀ */
#tarteaucitronRoot button:focus,
#tarteaucitronRoot a:focus {
    outline: 2px solid var(--atlas-primary) !important;
    outline-offset: 2px !important;
}

/* Miglioramento contrasto per accessibilità */
#tarteaucitronRoot .tarteaucitronH1 {
    color: var(--atlas-dark-gray) !important;
}

#tarteaucitronRoot .tarteaucitronDetails {
    color: var(--atlas-light-gray) !important;
}

/* STATI SPECIALI */
/* Banner ridotto dopo consenso */
#tarteaucitronRoot #tarteaucitronAlertSmall {
    background: var(--atlas-primary) !important;
    color: white !important;
    border-radius: 12px !important;
    font-family: 'Poppins', sans-serif !important;
}

/* Messaggi di stato */
#tarteaucitronRoot .tarteaucitronAllow {
    color: var(--atlas-primary) !important;
    font-weight: 500 !important;
}

#tarteaucitronRoot .tarteaucitronDeny {
    color: #DC2626 !important;
    font-weight: 500 !important;
}
