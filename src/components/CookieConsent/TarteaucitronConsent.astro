---
/**
 * Componente tarteaucitronjs per gestione cookie GDPR
 * Sostituisce il sistema di cookie consent precedente con una soluzione più completa
 * 
 * Features:
 * - Conforme GDPR/CCPA
 * - Gestione granulare dei servizi
 * - Personalizzazione brand Atlas
 * - Traduzioni italiane complete
 * - Responsive design
 */
---

<!-- tarteaucitronjs CSS -->
<link rel="stylesheet" href="/js/tarteaucitron/css/tarteaucitron.css" />
<!-- Stili personalizzati Atlas -->
<link rel="stylesheet" href="/js/tarteaucitron/atlas-custom.css" />

<!-- tarteaucitronjs Core Script -->
<script is:inline src="/js/tarteaucitron/tarteaucitron.js"></script>

<!-- Configurazione personalizzata Atlas -->
<script is:inline src="/js/tarteaucitron/tarteaucitron-config.js"></script>

<!-- Inizializzazione servizi -->
<script is:inline>
// Attende che tarteaucitron sia completamente caricato
document.addEventListener('DOMContentLoaded', function() {
    // Verifica che tarteaucitron sia disponibile
    if (typeof tarteaucitron !== 'undefined') {
        
        // Google Analytics 4 (se presente)
        if (typeof gtag !== 'undefined' || document.querySelector('[data-gtag]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('gtag');
        }
        
        // YouTube (se presente)
        if (document.querySelector('iframe[src*="youtube.com"], iframe[src*="youtu.be"]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('youtube');
        }
        
        // Calendly (se presente)
        if (document.querySelector('[data-calendly], iframe[src*="calendly.com"]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('calendly');
        }
        
        // MailerLite (se presente)
        if (document.querySelector('[data-mailerlite], script[src*="mailerlite"]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('mailerlite');
        }
        
        // Facebook Pixel (se presente)
        if (typeof fbq !== 'undefined' || document.querySelector('[data-facebook-pixel]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('facebookpixel');
        }
        
        // Google Maps (se presente)
        if (document.querySelector('iframe[src*="google.com/maps"], [data-google-maps]')) {
            (tarteaucitron.job = tarteaucitron.job || []).push('googlemaps');
        }
        
        // Swiper (locale, sempre consentito)
        if (document.querySelector('.swiper, [data-swiper]')) {
            // Swiper è locale, non richiede consenso
            console.log('🎠 Swiper rilevato - nessun consenso richiesto (locale)');
        }
        
        console.log('🍪 tarteaucitron servizi inizializzati:', tarteaucitron.job);
    } else {
        console.error('❌ tarteaucitron non disponibile');
    }
});

// Gestione View Transitions di Astro
document.addEventListener('astro:after-swap', function() {
    // Reinizializza tarteaucitron dopo navigazione
    if (typeof tarteaucitron !== 'undefined' && tarteaucitron.reloadThePage) {
        tarteaucitron.reloadThePage();
    }
});

// Gestione eventi personalizzati
document.addEventListener('tarteaucitron.ready', function() {
    console.log('🍪 tarteaucitron pronto');
});

document.addEventListener('tarteaucitron.services.loaded', function(event) {
    console.log('🔧 Servizio caricato:', event.detail);
});
</script>

<!-- Stili aggiuntivi per integrazione con il tema Atlas -->
<style>
    /* Assicura che l'icona floating non interferisca con altri elementi */
    #tarteaucitronRoot #tarteaucitronIcon {
        z-index: 9999 !important;
    }
    
    /* Assicura che il banner non interferisca con la navigazione */
    #tarteaucitronRoot #tarteaucitronAlertBig {
        z-index: 9998 !important;
    }
    
    /* Nasconde il banner se l'utente ha già fatto una scelta */
    body.tarteaucitron-consent-given #tarteaucitronRoot #tarteaucitronAlertBig {
        display: none !important;
    }
    
    /* Stile per dispositivi mobili */
    @media (max-width: 640px) {
        #tarteaucitronRoot #tarteaucitronIcon {
            bottom: 80px !important; /* Evita sovrapposizione con sticky bar mobile */
        }
    }
    
    /* Animazioni smooth */
    #tarteaucitronRoot * {
        transition: all 0.2s ease !important;
    }
    
    /* Focus accessibility */
    #tarteaucitronRoot button:focus,
    #tarteaucitronRoot a:focus {
        outline: 2px solid #006A71 !important;
        outline-offset: 2px !important;
    }
</style>

<!-- Preload per performance -->
<link rel="preload" href="/js/tarteaucitron/tarteaucitron.js" as="script">
<link rel="preload" href="/js/tarteaucitron/tarteaucitron-config.js" as="script">

<!-- Fallback per browser senza JavaScript -->
<noscript>
    <div style="
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background: #F8F7F4;
        border: 1px solid #E5E7EB;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        z-index: 9999;
        font-family: 'Poppins', sans-serif;
        color: #212121;
        text-align: center;
    ">
        <p style="margin: 0 0 0.5rem 0; font-weight: 600;">Cookie e Privacy</p>
        <p style="margin: 0; font-size: 0.9rem; color: #6B7280;">
            Questo sito utilizza cookie. JavaScript è necessario per la gestione delle preferenze.
            <a href="/privacy-policy" style="color: #006A71; text-decoration: underline;">
                Leggi la Privacy Policy
            </a>
        </p>
    </div>
</noscript>
