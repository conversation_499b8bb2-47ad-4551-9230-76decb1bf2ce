---
import { type CollectionEntry } from "astro:content";

// component imports
import BaseHead from "@layouts/BaseHead.astro";
import Nav from "@components/Nav/Nav.astro";
import Footer from "@components/Footer/Footer.astro";
import TarteaucitronConsent from "@components/CookieConsent/TarteaucitronConsent.astro";
import MobileNavContent from "@components/Nav/MobileNav/MobileNavContent.astro";

// style import
import "@/styles/global.css";
// data
import siteSettings from "@config/siteSettings.json";
import { type LocationData } from "@config/locations.ts";

// heroImage and authorData are defined on blog posts
// authorData could also be defined on the about author page
interface Props {
	type?: "blog" | "general" | "category";
	title: string;
	description: string;
	image?: ImageMetadata;
	authorsData?: CollectionEntry<"authors">[];
	postFrontmatter?: CollectionEntry<"blog">["data"];
	category?: string;
	postCount?: number;
	noindex?: boolean; // you need to opt-in to no indexing, as it hides the page from search engines
	primaryLocation?: LocationData; // New optional location prop
}

const {
	type = "general",
	title,
	description,
	image,
	authorsData,
	postFrontmatter,
	category,
	postCount,
	noindex = false,
	primaryLocation,
} = Astro.props as Props;

const currLocale = "it";
---

<!doctype html>
<html lang={currLocale} transition:animate="fade">
	<head>
		<BaseHead
			type={type}
			title={title}
			description={description}
			image={image ? image : undefined}
			authors={authorsData ? authorsData : undefined}
			postFrontmatter={postFrontmatter ? postFrontmatter : undefined}
			category={category}
			postCount={postCount}
			noindex={noindex}
			primaryLocation={primaryLocation}
		/>
	</head>
	<body id="body" class={` ${siteSettings.useAnimations === true ? "use-animations" : ""}`}>
		<!-- put cookie consent first so it is seen first by screen readers -->
		<TarteaucitronConsent />

		<!-- Mobile Menu Content -->
		<MobileNavContent />

		<div class="min-h-[100lvh]">
			<Nav />
			<main>
				<slot />
			</main>
		</div>
		<Footer />



		<!-- Scroll animations -->
		<script>
			import siteSettings from "@config/siteSettings.json";

			import AOS from "@js/aos/aos";

			if (siteSettings.useAnimations === true) {
				// runs on initial page load
				AOS.init({ once: true, duration: 0.75, distance: 100, offset: 120 });

				// runs on view transitions navigation
				document.addEventListener("astro:after-swap", AOS.refreshHard);
			}
		</script>
	</body>
</html>